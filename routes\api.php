<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Middleware\VerifyCsrfToken;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\PisoController;
use App\Http\Controllers\Api\ClienteController;
use App\Http\Controllers\Api\AlquilerController;
use App\Http\Controllers\Api\TrasteroController;
use App\Http\Controllers\Api\DashboardController;
use App\Http\Controllers\Api\DocumentoController;
use App\Http\Controllers\Api\GastoEdificioController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Rutas públicas (sin autenticación y sin CORS)
Route::withoutMiddleware([\Illuminate\Http\Middleware\HandleCors::class])->group(function () {
    //Route::post('/login', [AuthController::class, 'login']);
    //Route::post('/register', [AuthController::class, 'register']);

    // Ruta para obtener token CSRF (para SPA)
    Route::get('/csrf-token', function () {
        return response()->json(['csrf_token' => csrf_token()]);
    });
});

Route::withoutMiddleware([\Illuminate\Http\Middleware\HandleCors::class])
    ->middleware(\App\Http\Middleware\SanctumCors::class)
    ->middleware('auth:sanctum')
    ->withoutMiddleware([VerifyCsrfToken::class])
    ->group(function () {

    Route::get('clientes', [ClienteController::class, 'index']);
        
    Route::post('/trasteros/create', [TrasteroController::class, 'create']);
    Route::post('/trasteros/update/{id}', [TrasteroController::class, 'update']);
    Route::get('/trasteros/ver/{id}', [TrasteroController::class, 'ver']);

})
;


// Rutas protegidas con autenticación (sin CORS)
Route::withoutMiddleware([\Illuminate\Http\Middleware\HandleCors::class])
    ->middleware('auth:sanctum')->group(function () {

    // Usuario autenticado
    Route::get('/user', function (Request $request) {
        return $request->user();
    });

    Route::post('/logout', [AuthController::class, 'logout']);

    // === PISOS ===
    Route::get('pisos', [PisoController::class, 'index']);
    Route::get('pisos-disponibles', [PisoController::class, 'disponibles']);
    Route::get('pisos-ocupados', [PisoController::class, 'ocupados']);
    Route::get('pisos/buscar', [PisoController::class, 'buscar']);
    Route::get('pisos/{piso}/historial', [PisoController::class, 'historial']);
    Route::patch('pisos/{piso}/cambiar-estado', [PisoController::class, 'cambiarEstado']);
    Route::get('pisos/estadisticas', [PisoController::class, 'estadisticas']);
    
    // === TRASTEROS ===
    Route::get('trasteros', [TrasteroController::class, 'index']);
    Route::get('trasteros-disponibles', [TrasteroController::class, 'disponibles']);
    Route::get('trasteros-ocupados', [TrasteroController::class, 'ocupados']);
    Route::get('trasteros-tipo/{tipoId}', [TrasteroController::class, 'porTipo']);
    Route::get('trasteros/buscar', [TrasteroController::class, 'buscar']);
    Route::get('trasteros/{trastero}/historial', [TrasteroController::class, 'historial']);
    Route::patch('trasteros/{trastero}/cambiar-estado', [TrasteroController::class, 'cambiarEstado']);
    Route::get('trasteros/estadisticas', [TrasteroController::class, 'estadisticas']);

    // === CLIENTES ===
    Route::get('clientes-activos', [ClienteController::class, 'activos']);
    Route::get('clientes-posibles', [ClienteController::class, 'posibles']);
    Route::get('clientes-confirmados', [ClienteController::class, 'confirmados']);
    Route::patch('clientes/{cliente}/confirmar', [ClienteController::class, 'confirmar']);

    // === DOCUMENTOS ===
    Route::get('documentos', [DocumentoController::class, 'index']);
    Route::get('documentos/download/{documento}', [DocumentoController::class, 'download']);
    Route::get('documentos-tipo/{tipo}', [DocumentoController::class, 'porTipo']);
    Route::get('documentos-pendientes', [DocumentoController::class, 'pendientes']);
    Route::post('documentos/upload-multiple', [DocumentoController::class, 'uploadMultiple']);
    Route::get('documentos/{documento}/versiones', [DocumentoController::class, 'versiones']);
    Route::post('documentos/{documento}/nueva-version', [DocumentoController::class, 'nuevaVersion']);
    Route::patch('documentos/{documento}/cambiar-estado', [DocumentoController::class, 'cambiarEstado']);
    Route::get('documentos/buscar', [DocumentoController::class, 'buscar']);

    // === GASTOS EDIFICIO ===
    Route::get('gastos-edificio', [GastoEdificioController::class, 'index']);
    Route::patch('gastos-edificio/{gastoEdificio}/marcar-pagado', [GastoEdificioController::class, 'marcarPagado']);
    Route::get('gastos-pendientes', [GastoEdificioController::class, 'pendientes']);
    Route::get('gastos-vencidos', [GastoEdificioController::class, 'vencidos']);
    Route::get('gastos-tipo/{tipo}', [GastoEdificioController::class, 'porTipo']);
    Route::get('gastos-recurrentes', [GastoEdificioController::class, 'recurrentes']);

    // === ALQUILERES ===
    Route::get('alquileres', [AlquilerController::class, 'index']);
    Route::get('alquileres-pendientes', [AlquilerController::class, 'pendientes']);
    Route::get('alquileres-vencidos', [AlquilerController::class, 'vencidos']);
    Route::get('alquileres-al-dia', [AlquilerController::class, 'alDia']);
    Route::get('alquileres-parciales', [AlquilerController::class, 'parciales']);
    Route::patch('alquileres/{alquiler}/registrar-pago', [AlquilerController::class, 'registrarPago']);
    Route::post('alquileres/actualizar-estados-pago', [AlquilerController::class, 'actualizarEstadosPago']);
    Route::get('alquileres/buscar', [AlquilerController::class, 'buscar']);
    Route::get('alquileres/{alquiler}/historial-pagos', [AlquilerController::class, 'historialPagos']);
    Route::post('alquileres/generar-factura/{alquiler}', [AlquilerController::class, 'generarFactura']);
    Route::post('alquileres/enviar-recordatorio/{alquiler}', [AlquilerController::class, 'enviarRecordatorio']);
    Route::get('alquileres/estadisticas-pagos', [AlquilerController::class, 'estadisticasPagos']);

    // === DASHBOARD / ESTADÍSTICAS ===
    Route::get('dashboard/resumen', [DashboardController::class, 'resumen']);
    Route::get('dashboard/pagos-pendientes', [DashboardController::class, 'pagosPendientes']);
    Route::get('dashboard/ocupacion', [DashboardController::class, 'estadisticasOcupacion']);
    Route::get('dashboard/ingresos', [DashboardController::class, 'estadisticasIngresos']);

    // === REPORTES Y ESTADÍSTICAS AVANZADAS ===
    Route::prefix('reportes')->group(function () {
        Route::get('ingresos-detallado', [DashboardController::class, 'reporteIngresosDetallado']);
        Route::get('ocupacion-historica', [DashboardController::class, 'ocupacionHistorica']);
        Route::get('clientes-morosos', [DashboardController::class, 'clientesMorosos']);
        Route::get('rentabilidad', [DashboardController::class, 'reporteRentabilidad']);
        Route::get('exportar/{tipo}', [DashboardController::class, 'exportarReporte']);
    });

    // === NOTIFICACIONES ===
    Route::prefix('notificaciones')->group(function () {
        Route::get('/', [DashboardController::class, 'notificaciones']);
        Route::patch('{id}/marcar-leida', [DashboardController::class, 'marcarLeida']);
        Route::post('enviar-recordatorio/{tipo}/{id}', [DashboardController::class, 'enviarRecordatorio']);
    });

    // === BÚSQUEDA GLOBAL ===
    Route::get('busqueda-global', [DashboardController::class, 'busquedaGlobal']);
});

