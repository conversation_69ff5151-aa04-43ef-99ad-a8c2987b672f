[2025-07-11 12:11:37] local.DEBUG: SanctumCors middleware ejecutado  
[2025-07-11 12:11:38] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'date_to' in 'where clause' (Connection: mysql, SQL: select * from `periodos_pisos` where `date_to` is null and `periodos_pisos`.`id_cliente` in (1, 2, 3, 4)) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'date_to' in 'where clause' (Connection: mysql, SQL: select * from `periodos_pisos` where `date_to` is null and `periodos_pisos`.`id_cliente` in (1, 2, 3, 4)) at C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3131): Illuminate\\Database\\Connection->select()
#3 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(872): Illuminate\\Database\\Query\\Builder->get()
#7 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(854): Illuminate\\Database\\Eloquent\\Builder->getModels()
#8 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(212): Illuminate\\Database\\Eloquent\\Builder->get()
#9 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(175): Illuminate\\Database\\Eloquent\\Relations\\Relation->get()
#10 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(920): Illuminate\\Database\\Eloquent\\Relations\\Relation->getEager()
#11 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(889): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelation()
#12 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(855): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelations()
#13 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1095): Illuminate\\Database\\Eloquent\\Builder->get()
#14 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\app\\Http\\Controllers\\Api\\ClienteController.php(47): Illuminate\\Database\\Eloquent\\Builder->paginate()
#15 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\ClienteController->index()
#16 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#17 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#18 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#19 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#20 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#21 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#23 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#25 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#27 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}()
#29 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#31 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#33 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#34 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#36 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#38 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(190): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}()
#40 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(25): Illuminate\\Pipeline\\Pipeline->then()
#42 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle()
#43 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#45 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#46 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#47 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#48 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#49 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#50 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#52 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#53 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#55 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#56 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#58 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#60 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#61 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#62 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#63 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#64 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#65 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#66 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#67 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#68 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#69 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#70 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#71 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#72 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#73 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'date_to' in 'where clause' at C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare()
#1 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3131): Illuminate\\Database\\Connection->select()
#5 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#8 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(872): Illuminate\\Database\\Query\\Builder->get()
#9 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(854): Illuminate\\Database\\Eloquent\\Builder->getModels()
#10 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(212): Illuminate\\Database\\Eloquent\\Builder->get()
#11 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(175): Illuminate\\Database\\Eloquent\\Relations\\Relation->get()
#12 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(920): Illuminate\\Database\\Eloquent\\Relations\\Relation->getEager()
#13 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(889): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelation()
#14 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(855): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelations()
#15 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1095): Illuminate\\Database\\Eloquent\\Builder->get()
#16 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\app\\Http\\Controllers\\Api\\ClienteController.php(47): Illuminate\\Database\\Eloquent\\Builder->paginate()
#17 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\ClienteController->index()
#18 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#19 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#20 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#21 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#22 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#23 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#25 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#27 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#29 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}()
#31 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#33 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#35 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#36 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#38 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#40 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(190): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}()
#42 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(25): Illuminate\\Pipeline\\Pipeline->then()
#44 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle()
#45 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#47 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#48 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#49 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#50 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#51 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#52 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#54 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#55 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#57 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#58 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#60 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#61 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#62 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#63 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#64 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#65 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#66 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#67 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#68 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#69 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#70 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#71 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#72 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#73 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#74 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#75 {main}
"} 
