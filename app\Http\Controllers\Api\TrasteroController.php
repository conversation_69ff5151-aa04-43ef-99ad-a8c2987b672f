<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\TrasteroResource;
use App\Trastero;
use App\Periodo;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class TrasteroController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Trastero::with(['periodoActivo.cliente', 'documentos', 'tipoTrastero']);

        // Filtros
        if ($request->has('ocupado')) {
            $query->where('ocupado', $request->ocupado);
        }

        if ($request->has('tipo_trastero')) {
            $query->where('tipo_trastero', $request->tipo_trastero);
        }

        if ($request->has('precio_min')) {
            $query->where('price', '>=', $request->precio_min);
        }

        if ($request->has('precio_max')) {
            $query->where('price', '<=', $request->precio_max);
        }

        if ($request->has('numero')) {
            $query->where('num', 'like', '%' . $request->numero . '%');
        }

        $trasteros = $query->paginate($request->get('per_page', 15));

        return TrasteroResource::collection($trasteros);
    }

    public function ver(Trastero $id) {
        $trastero = Trastero::find($id);

        LOG::debug('trastero');
        LOG::debug($trastero);

        return response()->json(['trastero' => $trastero], ( count($trastero) ) ? 200 : 204);
    }

    public function update(Request $request, int $id) {
        // Validar los datos de entrada

        LOG::debug('request');
        LOG::debug($request->all());

        LOG::debug('data');
        LOG::debug($request->data);
/*
        $validated = $request->validate([
            'num' => 'string|max:255|unique:trasteros,num,' . $trastero->num,
            'titulo' => 'string|max:255',
            'descripcion' => 'nullable|string',
            'price' => 'numeric|min:0',
            'ocupado' => 'string|in:disponible,ocupado,mantenimiento,reservado',
            'tipo_trastero' => 'sometimes|integer|exists:tipos_trasteros,id',

            'numero_planta' => 'integer',
            'metros_cuadrados' => 'numeric',

        ]);
*/

        $trastero = Trastero::find($id);
        //$trastero->update($validated);
        $trastero->update($request->data);

        $trastero = Trastero::find($id);

        return response()->json(['trastero' => $trastero], 200);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function create(Request $request)
    {
        $validated = $request->validate([
            'num' => 'required|string|max:255|unique:trasteros,num',
            'titulo' => 'required|string|max:255',
            'descripcion' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'ocupado' => 'required|string|in:disponible,ocupado,mantenimiento,reservado',
            'tipo_trastero' => 'required|integer|exists:tipos_trasteros,id',

            'numero_planta' => 'integer',
            'metros_cuadrados' => 'numeric'
        ]);

        LOG::debug('validated store');
        LOG::debug($validated);

        $trastero = Trastero::create($request->data);

        return new TrasteroResource($trastero->load(['documentos', 'tipoTrastero']));
    }

    /**
     * Display the specified resource.
     */
    public function show(Trastero $trastero)
    {
        return new TrasteroResource($trastero->load([
            'periodos.cliente',
            'periodoActivo.cliente',
            'documentos.usuario',
            'tipoTrastero'
        ]));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Trastero $trastero): JsonResponse
    {
        // Verificar que no tenga períodos activos
        if ($trastero->periodoActivo) {
            return response()->json([
                'message' => 'No se puede eliminar un trastero con período activo'
            ], 422);
        }

        $trastero->delete();

        return response()->json(['message' => 'Trastero eliminado correctamente']);
    }

    /**
     * Get available trasteros
     */
    public function disponibles()
    {
        $trasteros = Trastero::disponibles()->with(['documentos', 'tipoTrastero'])->get();
        return TrasteroResource::collection($trasteros);
    }

    /**
     * Get occupied trasteros
     */
    public function ocupados()
    {
        $trasteros = Trastero::ocupados()->with(['periodoActivo.cliente', 'tipoTrastero'])->get();
        return TrasteroResource::collection($trasteros);
    }

    /**
     * Get trasteros by type
     */
    public function porTipo(int $tipoId)
    {
        $trasteros = Trastero::where('tipo_trastero', $tipoId)
                            ->with(['documentos', 'tipoTrastero', 'periodoActivo.cliente'])
                            ->get();

        return TrasteroResource::collection($trasteros);
    }

    /**
     * Búsqueda avanzada de trasteros
     */
    public function buscar(Request $request): JsonResponse
    {
        $query = Trastero::with(['periodoActivo.cliente', 'documentos', 'tipoTrastero']);

        // Filtros de búsqueda
        if ($request->filled('q')) {
            $searchTerm = $request->q;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('num', 'like', "%{$searchTerm}%")
                  ->orWhere('titulo', 'like', "%{$searchTerm}%")
                  ->orWhere('descripcion', 'like', "%{$searchTerm}%");
            });
        }

        if ($request->filled('estado')) {
            $query->where('ocupado', $request->estado === 'ocupado');
        }

        if ($request->filled('tipo_trastero')) {
            $query->where('tipo_trastero', $request->tipo_trastero);
        }

        if ($request->filled('precio_min')) {
            $query->where('price', '>=', $request->precio_min);
        }

        if ($request->filled('precio_max')) {
            $query->where('price', '<=', $request->precio_max);
        }

        if ($request->filled('cliente_id')) {
            $query->whereHas('periodoActivo', function ($q) use ($request) {
                $q->where('id_cliente', $request->cliente_id);
            });
        }

        // Ordenamiento
        $sortBy = $request->get('sort_by', 'num');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        $trasteros = $query->paginate($request->get('per_page', 15));

        return response()->json($trasteros);
    }

    /**
     * Obtener historial de un trastero
     */
    public function historial(Trastero $trastero): JsonResponse
    {
        $historial = $trastero->periodos()
                             ->with(['cliente'])
                             ->orderBy('date_from', 'desc')
                             ->get();

        return response()->json($historial);
    }

    /**
     * Cambiar estado de ocupación de un trastero
     */
    public function cambiarEstado(Request $request, Trastero $trastero): JsonResponse
    {
        $validated = $request->validate([
            'ocupado' => 'required|string|in:disponible,ocupado,mantenimiento,reservado',
            'motivo' => 'nullable|string|max:255'
        ]);

        $trastero->update(['ocupado' => $validated['ocupado']]);

        return response()->json([
            'message' => 'Estado del trastero actualizado correctamente',
            'trastero' => $trastero->load(['documentos', 'tipoTrastero'])
        ]);
    }

    /**
     * Obtener estadísticas de trasteros
     */
    public function estadisticas(): JsonResponse
    {
        $stats = [
            'total' => Trastero::count(),
            'disponibles' => Trastero::where('ocupado', false)->count(),
            'ocupados' => Trastero::where('ocupado', true)->count(),
            'por_tipo' => Trastero::selectRaw('tipo_trastero, COUNT(*) as total')
                                 ->groupBy('tipo_trastero')
                                 ->with('tipoTrastero')
                                 ->get(),
            'precio_promedio' => Trastero::avg('price'),
            'precio_min' => Trastero::min('price'),
            'precio_max' => Trastero::max('price'),
            'ocupacion_porcentaje' => round((Trastero::where('ocupado', true)->count() / Trastero::count()) * 100, 2)
        ];

        return response()->json($stats);
    }
}
